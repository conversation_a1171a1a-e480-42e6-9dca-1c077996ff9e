import React, { useState } from 'react';
import './AuthorRanking.css';

const AuthorRanking = () => {
  const [activeTab, setActiveTab] = useState('authors');

  const authors = [
    {
      rank: 1,
      name: "<PERSON><PERSON><PERSON> Aditya <PERSON>",
      points: 1253,
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 2,
      name: "<PERSON><PERSON><PERSON>",
      points: 1250,
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 3,
      name: "<PERSON><PERSON><PERSON>",
      points: 1234,
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 4,
      name: "<PERSON><PERSON>",
      points: 1108,
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 5,
      name: "Bhavna Goyal",
      points: 1000,
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 6,
      name: "Nandini Kumar",
      points: 991,
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 7,
      name: "Jeevan Das",
      points: 856,
      avatar: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=50&h=50&fit=crop&crop=face"
    }
  ];

  return (
    <div className="author-ranking-container">
      {/* Header */}
      <div className="ranking-header">
        <div className="header-content">
          <div className="logo-section">
            <span className="logo">SKRIVEE</span>
          </div>
          <div className="nav-section">
            <span>Romance</span>
            <span>Fantasy</span>
            <span>Mystery</span>
            <span>Browse All</span>
          </div>
          <div className="search-section">
            <input type="text" placeholder="Authors" className="search-input" />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="ranking-main">
        {/* Sidebar */}
        <div className="sidebar">
          <div className="sidebar-item active">
            <span className="icon">📝</span>
            <span>Skrivee</span>
          </div>
          <div className="sidebar-item">
            <span className="icon">📊</span>
            <span>Ranking</span>
          </div>
          <div className="sidebar-item">
            <span className="icon">🎯</span>
            <span>Pitch</span>
          </div>
          <div className="sidebar-item">
            <span className="icon">💬</span>
            <span>Messages</span>
          </div>
          <div className="sidebar-item">
            <span className="icon">🔔</span>
            <span>Notifications</span>
          </div>
          <div className="sidebar-item">
            <span className="icon">📈</span>
            <span>Dashboard</span>
          </div>
          <div className="sidebar-item">
            <span className="icon">❓</span>
            <span>Help</span>
          </div>
          <div className="sidebar-item">
            <span className="icon">⚙️</span>
            <span>Settings</span>
          </div>
          <div className="sidebar-item">
            <span className="icon">👤</span>
            <span>Profile</span>
          </div>
          <div className="sidebar-item">
            <span className="icon">🚪</span>
            <span>Logout</span>
          </div>
        </div>

        {/* Content Area */}
        <div className="content-area">
          {/* Tab Navigation */}
          <div className="tab-navigation">
            <button 
              className={`tab-btn ${activeTab === 'authors' ? 'active' : ''}`}
              onClick={() => setActiveTab('authors')}
            >
              Top Authors
            </button>
            <button 
              className={`tab-btn ${activeTab === 'publications' ? 'active' : ''}`}
              onClick={() => setActiveTab('publications')}
            >
              Top Publication
            </button>
          </div>

          {/* Ranking Table */}
          <div className="ranking-table">
            <div className="table-header">
              <div className="header-cell rank-col">Rank</div>
              <div className="header-cell name-col">Name</div>
              <div className="header-cell points-col">Points</div>
              <div className="header-cell action-col"></div>
            </div>

            <div className="table-body">
              {authors.map((author) => (
                <div key={author.rank} className="table-row">
                  <div className="cell rank-cell">{author.rank}</div>
                  <div className="cell name-cell">
                    <img src={author.avatar} alt={author.name} className="author-avatar" />
                    <span className="author-name">{author.name}</span>
                  </div>
                  <div className="cell points-cell">{author.points}</div>
                  <div className="cell action-cell">
                    <button className="follow-btn">Follow</button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Sidebar - Promotional Cards */}
        <div className="right-sidebar">
          <div className="promo-card">
            <div className="promo-content">
              <h3>Take your website to the next level</h3>
              <button className="get-started-btn">Get started</button>
            </div>
            <div className="promo-chart">
              <div className="chart-placeholder"></div>
            </div>
          </div>

          <div className="promo-card">
            <div className="promo-content">
              <h3>Take your website to the next level</h3>
              <button className="get-started-btn">Get started</button>
            </div>
            <div className="promo-image">
              <div className="image-placeholder"></div>
            </div>
          </div>

          <div className="promo-card">
            <div className="promo-content">
              <h3>Take your website to the next level</h3>
              <button className="get-started-btn">Get started</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthorRanking;
