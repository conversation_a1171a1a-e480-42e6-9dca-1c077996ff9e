/* Author Ranking Page Styles */
.author-ranking-container {
  min-height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Styles */
.ranking-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  max-width: 1400px;
  margin: 0 auto;
}

.logo-section .logo {
  font-size: 24px;
  font-weight: bold;
  color: #1e293b;
  text-decoration: none;
}

.nav-section {
  display: flex;
  gap: 30px;
}

.nav-section span {
  color: #64748b;
  cursor: pointer;
  font-weight: 500;
  transition: color 0.2s;
}

.nav-section span:hover {
  color: #1e293b;
}

.search-section {
  position: relative;
}

.search-input {
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  width: 200px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: #3b82f6;
}

/* Main Layout */
.ranking-main {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  gap: 20px;
  padding: 20px;
}

/* Sidebar Styles */
.sidebar {
  width: 200px;
  background: white;
  border-radius: 12px;
  padding: 20px 0;
  height: fit-content;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #64748b;
  font-weight: 500;
}

.sidebar-item:hover {
  background: #f1f5f9;
}

.sidebar-item.active {
  background: #eff6ff;
  color: #3b82f6;
  border-right: 3px solid #3b82f6;
}

.sidebar-item .icon {
  font-size: 18px;
}

/* Content Area */
.content-area {
  flex: 1;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.tab-btn {
  flex: 1;
  padding: 16px 24px;
  background: transparent;
  border: none;
  color: white;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.tab-btn.active {
  background: rgba(255, 255, 255, 0.2);
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Ranking Table */
.ranking-table {
  padding: 0;
}

.table-header {
  display: grid;
  grid-template-columns: 80px 1fr 120px 120px;
  background: #40e0d0;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.header-cell {
  padding: 16px 20px;
  text-align: left;
}

.table-body {
  background: white;
}

.table-row {
  display: grid;
  grid-template-columns: 80px 1fr 120px 120px;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s;
}

.table-row:hover {
  background: #f8fafc;
}

.cell {
  padding: 16px 20px;
  display: flex;
  align-items: center;
}

.rank-cell {
  font-weight: 600;
  font-size: 18px;
  color: #1e293b;
}

.name-cell {
  gap: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e2e8f0;
}

.author-name {
  font-weight: 500;
  color: #1e293b;
}

.points-cell {
  font-weight: 600;
  color: #059669;
  font-size: 16px;
}

.follow-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.follow-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Right Sidebar */
.right-sidebar {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.promo-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.promo-content h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
}

.get-started-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.get-started-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.promo-chart, .promo-image {
  margin-top: 16px;
  height: 80px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder, .image-placeholder {
  width: 60px;
  height: 40px;
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 4px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .right-sidebar {
    display: none;
  }
}

@media (max-width: 768px) {
  .ranking-main {
    flex-direction: column;
    padding: 10px;
  }
  
  .sidebar {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 10px 0;
  }
  
  .sidebar-item {
    white-space: nowrap;
    min-width: fit-content;
  }
  
  .table-header, .table-row {
    grid-template-columns: 60px 1fr 80px 80px;
  }
  
  .header-cell, .cell {
    padding: 12px 8px;
    font-size: 14px;
  }
}
